'use client';
import React, { useState, useEffect, useContext } from 'react';
import { Box, Typography } from '@mui/material';

import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import CommonUserDetails from '@/components/UI/CommonUserDetails';
import ContentLoader from '@/components/UI/ContentLoader';
import AuthContext from '@/helper/authcontext';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { staticOptions } from '@/helper/common/staticOptions';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';
import { staffService } from '@/services/staffService';
import { branchService } from '@/services/branchService';

import '../reports.scss';

// Filter fields for staff reports
const filterFields = [
  {
    type: 'search',
    label: 'Search',
    name: 'search',
    placeholder: 'Search by name, email, or employment number',
  },
  {
    type: 'select',
    label: 'Branch',
    name: 'branch',
    placeholder: 'Select Branch',
    options: [], // Will be populated dynamically
  },
  {
    type: 'select',
    label: 'Department',
    name: 'department',
    placeholder: 'Select Department',
    options: [], // Will be populated dynamically
  },
  {
    type: 'select',
    label: 'Role',
    name: 'role',
    placeholder: 'Select Role',
    options: [], // Will be populated dynamically
  },
  {
    type: 'select',
    label: 'Status',
    name: 'status',
    placeholder: 'Select Status',
    options: staticOptions.USER_STATUS_OPTIONS,
  },
  {
    type: 'select',
    label: 'Training Status',
    name: 'trainingStatus',
    placeholder: 'Select Training Status',
    options: staticOptions.TRAINING_FILTER_STATUS,
  },
  {
    type: 'select',
    label: 'Contract Status',
    name: 'contractStatus',
    placeholder: 'Select Contract Status',
    options: staticOptions.CONTRACT_FILTER_STATUS,
  },
  {
    type: 'select',
    label: 'Employee Type',
    name: 'filter_type',
    placeholder: 'Select Employee Type',
    options: [
      { label: 'Joining Date', value: 'joining_date' },
      { label: 'Old Employee', value: 'old_employee' },
      { label: 'Notice Period', value: 'notice_period' },
    ],
  },
  {
    type: 'date-range',
    label: 'Date Range',
    name: 'dateRange',
    placeholder: 'Select date range',
    format: 'MMM dd, yyyy',
    conditional: {
      dependsOn: 'filter_type',
      showWhen: ['joining_date', 'notice_period'],
    },
  },
];

// Status class functions
const getStatusClass = (status) => {
  const map = {
    rejected: 'failed',
    deleted: 'failed',
    cancelled: 'failed',
    ongoing: 'ongoing',
    pending: 'draft',
    active: 'active-onboarding',
    completed: 'active-onboarding',
    verified: 'active-onboarding',
  };
  return map[status] || 'success';
};

const getStatusClassOnboard = (status) => {
  const map = {
    ongoing: 'ongoing',
    pending: 'draft',
    completed: 'active-onboarding',
  };
  return map[status] || 'success';
};

export default function StaffUserReports() {
  const { authState, setUserdata, AllListsData, setAllListsData } =
    useContext(AuthContext);

  const [staffList, setStaffList] = useState([]);
  const [loader, setLoader] = useState(false);
  const [filters, setFilters] = useState({});
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [filterFieldsWithOptions, setFilterFieldsWithOptions] =
    useState(filterFields);

  // Sorting state
  const [sortOrder, setSortOrder] = useState({ key: '', value: 'ASC' });

  const actionMenuItems = [];

  // CommonTable columns
  const columns = [
    {
      header: 'ID',
      accessor: 'employment_number',
      sortable: true,
      renderCell: (value) => (
        <Box className="d-flex align-center justify-center h100">
          {value ? value : '-'}
        </Box>
      ),
    },
    {
      header: 'User',
      accessor: 'user_full_name',
      sortable: false,
      renderCell: (_, row) => (
        <CommonUserDetails
          userData={row}
          searchValue={searchValue}
          page={page}
          rowsPerPage={rowsPerPage}
          setUserdata={setUserdata}
          authState={authState}
          navigationProps={{ staff: true }}
        />
      ),
    },
    {
      header: 'Branch / Dep.',
      accessor: 'branch_department',
      sortable: false,
      renderCell: (_, row) => <BranchDepartmentDisplay row={row} />,
    },
    {
      header: 'Joining Date',
      accessor: 'user_joining_date',
      sortable: false,
      renderCell: (value) => (
        <Box className="d-flex align-center justify-center h100">
          {DateFormat(value, 'date')}
        </Box>
      ),
    },
    {
      header: 'Profile Status',
      accessor: 'user_status',
      sortable: false,
      renderCell: (value) => (
        <Box className="d-flex align-center justify-center h100 text-capital">
          <Typography
            className={`sub-title-text ${getStatusClass(value)} fw600`}
          >
            {value}
          </Typography>
        </Box>
      ),
    },
    {
      header: 'Training Status',
      accessor: 'user_track_status',
      sortable: false,
      renderCell: (value) => (
        <Box className="d-flex align-center justify-center h100 text-capital">
          <Typography
            className={`sub-title-text ${getStatusClassOnboard(value)} fw600`}
          >
            {value}
          </Typography>
        </Box>
      ),
    },
    {
      header: 'Contract Status',
      accessor: 'user_contract',
      sortable: false,
      renderCell: (_, row) => {
        const contractStatus =
          row?.user_contract?.is_confirm_sign === 1
            ? row?.is_probation === 1
              ? 'Probation'
              : 'Active'
            : row?.user_contract?.is_confirm_sign === 0
              ? 'Awaiting Signature'
              : 'Pending';

        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            <Typography
              className={`sub-title-text ${getStatusClass(contractStatus?.toLowerCase())} fw600`}
            >
              {contractStatus}
            </Typography>
          </Box>
        );
      },
    },
  ];

  // Load branch data into AllListsData for BranchDepartmentDisplay colors
  const loadBranchDataForColors = async () => {
    try {
      const branchAndDeptData =
        await branchService.getBranchAndDepartmentLists();
      setAllListsData({
        ...AllListsData,
        SelectBranchList: branchAndDeptData?.selectBranchList,
        ActiveBranchList: branchAndDeptData?.activeBranchList,
        SelectDepartmentList: branchAndDeptData?.selectDepartmentList,
        ActiveDepartmentList: branchAndDeptData?.activeDepartmentList,
      });
    } catch (error) {
      console.error('Error loading branch data for colors:', error);
    }
  };

  // Load filter options
  const loadFilterOptions = async () => {
    try {
      const [branches, departments, roles] = await Promise.all([
        staffService.getBranchList(),
        staffService.getDepartmentList(),
        staffService.getRoleList(),
      ]);

      // Update filter fields with options
      const updatedFields = filterFieldsWithOptions.map((field) => {
        if (field.name === 'branch') {
          return { ...field, options: branches };
        }
        if (field.name === 'department') {
          return { ...field, options: departments };
        }
        if (field.name === 'role') {
          return { ...field, options: roles };
        }
        return field;
      });

      setFilterFieldsWithOptions(updatedFields);
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  };

  // Get staff list from API
  const getStaffList = async (
    search = '',
    pageNo = 1,
    branch = '',
    role = '',
    department = '',
    statusValue = '',
    trainingStatus = '',
    contractStatus = '',
    Rpp = rowsPerPage,
    startDate = '',
    endDate = '',
    filterType = '',
    sortBy = '',
    sortOrderValue = '',
    showLoader = true
  ) => {
    if (showLoader) setLoader(true);
    try {
      let url =
        URLS?.GET_USER_LIST +
        `?isAdmin=false&search=${search}&page=${pageNo}&size=${Rpp}&branch_id=${branch}&status=${statusValue}&user_track_status=${trainingStatus}&contract_status=${contractStatus}&role_id=${role}&department_id=${department}`;

      // Add filter_type if provided
      if (filterType) url += `&filter_type=${filterType}`;

      // Add date range if provided
      if (startDate) url += `&start_date=${startDate}`;
      if (endDate) url += `&end_date=${endDate}`;

      // Add sorting parameters if provided
      if (sortBy) url += `&sort_by=${sortBy}`;
      if (sortOrderValue) url += `&sort_order=${sortOrderValue}`;

      const { status, data } = await axiosInstance.get(url);

      if (status === 200) {
        setStaffList(data?.userList || []);
        setTotalCount(data?.count || 0);
        setPage(pageNo);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setStaffList([]);
      setTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Handle filter application
  const handleApplyFilters = (values) => {
    setFilters(values);
    setSearchValue(values?.search || '');

    const startDate = values?.dateRange?.[0]
      ? new Date(values.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = values?.dateRange?.[1]
      ? new Date(values.dateRange[1]).toISOString().split('T')[0]
      : '';

    setPage(1);
    getStaffList(
      values?.search || '',
      1,
      values?.branch || '',
      values?.role || '',
      values?.department || '',
      values?.status || '',
      values?.trainingStatus || '',
      values?.contractStatus || '',
      rowsPerPage,
      startDate,
      endDate,
      values?.filter_type || '',
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPage(newPage);
    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    getStaffList(
      searchValue,
      newPage,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      filters?.status || '',
      filters?.trainingStatus || '',
      filters?.contractStatus || '',
      rowsPerPage,
      startDate,
      endDate,
      filters?.filter_type || '',
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    getStaffList(
      searchValue,
      1,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      filters?.status || '',
      filters?.trainingStatus || '',
      filters?.contractStatus || '',
      newRowsPerPage,
      startDate,
      endDate,
      filters?.filter_type || '',
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  // Handle sorting
  const handleSort = (key) => {
    const newOrder = sortOrder?.value === 'ASC' ? 'DESC' : 'ASC';
    const newSortOrder = { key, value: newOrder };
    setSortOrder(newSortOrder);
    setPage(1);

    const startDate = filters?.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters?.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    // Call API with new sort order without showing loader
    getStaffList(
      searchValue,
      1,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      filters?.status || '',
      filters?.trainingStatus || '',
      filters?.contractStatus || '',
      rowsPerPage,
      startDate,
      endDate,
      filters?.filter_type || '',
      key,
      newOrder,
      false // Don't show loader for sorting
    );
  };

  // useEffect hooks
  useEffect(() => {
    loadFilterOptions();
    loadBranchDataForColors(); // Load branch colors for BranchDepartmentDisplay
  }, []);

  useEffect(() => {
    getStaffList();
  }, []);

  return (
    <Box className="report-main-container">
      <>
        <FilterCollapse
          fields={filterFieldsWithOptions}
          onApply={handleApplyFilters}
          initialValues={filters}
        />

        <Box className="report-table-container">
          {loader ? (
            <ContentLoader />
          ) : (
            <CommonTable
              columns={columns}
              data={staffList}
              totalCount={totalCount}
              page={page}
              rowsPerPage={rowsPerPage}
              onPageChange={handlePageChange}
              onRowsPerPageChange={handleRowsPerPageChange}
              actionMenuItems={actionMenuItems}
              showPagination={true}
              onSort={handleSort}
              sortOrder={sortOrder}
            />
          )}
        </Box>
      </>
    </Box>
  );
}
